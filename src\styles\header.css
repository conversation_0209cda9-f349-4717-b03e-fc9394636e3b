header {
  display: flex;
  flex-direction: column;
  text-align: left;
  width: 100%;
  gap: 20px;
  padding: 50px 0 20px 34px;
}

header img {
  max-width: 150px;
}

header #header-category {
  padding-left: 20px;
  background-color: var(--accent-color);
  width: 100%;
  height: 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

header h1 {
  color: var(--text-secondary);
}

/* media for tablet */
@media (min-width: 768px) {
  header {
    padding: 5vh 0 5vh 80px;
  }
  header img {
    max-width: 262px;
  }
}

/*  Media for desktop */
@media (min-width: 1280px) {
  header {
    padding: 40px 0 20px 160px;
  }
  header img {
    max-width: 180px;
  }
}
