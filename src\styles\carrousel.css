.carousel-container {
  touch-action: none;
  position: relative;
  flex-direction: column;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  touch-action: pan-x;
  height: 100%;
  min-height: 0;
}

.swiper {
  display: flex;
  width: 100%;
  height: auto;
  max-height: 100%;
  overflow: visible !important;
  margin-left: 0px !important;
}

.swiper-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
}

.swiper-slide {
  height: 100%;
  text-align: center;
  border-radius: 12px;
  background-color: var(--secondary-color);
  padding: 14px;
  display: flex !important
;
  flex-direction: column;
}

.swiper-slide .clicked {
  cursor: pointer;
  transform: scale(0.85);
  transition: transform 0.1s ease-out;
}

.image-container {
  border-radius: 12px;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 90%;
}

.swiper-slide img {
  position: relative;
  width: auto;
  width: 100%;
  height: 100%;
  min-height: 0;
  top: 0;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
  backface-visibility: hidden;
  border-radius: 12px;
  object-fit: cover;
  object-position: bottom left;
  background-repeat: no-repeat;
}

.swiper-slide h1 {
  text-align: right;
  position: relative;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
}

.image-preview {
  position: absolute !important;
  padding-bottom: 20px;
  pointer-events: none;
}

.image-label {
  width: 100%;
}
.swiper-slide img {
  object-fit: cover;
  /* object-position: bottom center; */
  background-repeat: no-repeat;
}

/* media for tablet */
@media (min-width: 768px) {
  .swiper-slide {
    display: flex !important;
    flex-direction: column;
    flex: 0 0 auto; /* Important pour ne pas forcer un stretch */
  }

  .swiper-slide p {
    font-family: "Funnel Display";
    font-size: 2rem;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }

  .swiper-wrapper {
    justify-content: start; /* pour aligner à gauche */
    display: flex;
    width: 100%;
    height: 100% !important;
  }
}

@media (min-width: 1280px) {
  .carousel-container {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .swiper {
    display: flex !important;
    flex-wrap: wrap;
    gap: 24px;
    justify-content: center;
    align-items: stretch;
    width: 100%;
    margin: 0 auto;
    overflow: visible !important;
  }

  .swiper-wrapper {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center;
    align-items: stretch;
    transform: none !important;
    width: 100%;
  }

  .swiper-slide {
    flex: 0 0 calc((100% - (2 * 24px)) / 3);
    max-width: calc((100% - (2 * 24px)) / 3);
    height: auto;
    min-height: 220px;
    padding: 16px;
    background-color: var(--secondary-color);
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .swiper-slide p {
    font-size: 1.2rem;
    text-align: center;
    font-weight: bold;
    margin-top: 10px;
  }

  .image-container {
    padding: 0;
  }
}
