{"type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "npx vite build && firebase hosting:channel:deploy staging", "deploy": "npx vite build && firebase deploy --only hosting:yourte-in-garden"}, "devDependencies": {"eslint": "^9.22.0", "prettier": "^3.5.3", "vite": "^6.2.2", "vite-plugin-mkcert": "^1.17.8", "vite-plugin-static-copy": "^2.2.0"}, "dependencies": {"swiper": "^11.2.5"}}