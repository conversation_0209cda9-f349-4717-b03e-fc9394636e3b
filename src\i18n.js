export default class I18n {
  constructor(defaultLang = "fr") {
    this.lang =
      (navigator.language || navigator.userLanguage).split("-")[0] ||
      defaultLang;
    this.translations = {};
  }

  async fetchJsonSilently(lang) {
    try {
      const res = await fetch(`./locales/fr.json`);
      const contentType = res.headers.get("content-type");
      if (
        !res.ok ||
        !contentType ||
        !contentType.includes("application/json")
      ) {
        return null;
      }
      return await res.json();
    } catch {
      return null;
    }
  }

  async loadTranslations() {
    this.translations =
      (await this.fetchJsonSilently(this.lang)) ||
      (await this.fetchJsonSilently("fr")) ||
      {};
  }

  getTranslation(key) {
    return this.translations[key] || key;
  }

  applyTranslations() {
    document.querySelectorAll("[data-i18n]").forEach((element) => {
      const key = element.getAttribute("data-i18n");
      if (this.translations[key]) {
        element.innerText = this.translations[key];
      }
    });
  }
}

// Usage
// const i18n = new I18n();

// (async () => {
//     await i18n.loadTranslations();
//     i18n.applyTranslations(); // Update the DOM with the translated text
// })();
