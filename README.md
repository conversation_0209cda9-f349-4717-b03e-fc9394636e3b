# **Yourte in Garden Selector – Wonder Shop**

An **interactive yurt selection experience** integrated with **Wonder Shop**, allowing users to choose their ideal yurt configuration before viewing it in **Augmented Reality (AR)**. Users can select the **size** and **exterior finish** of the yurt before launching the AR preview.

## **Features**

- **Step-by-step selection process**: Users select the **yurt size** (17m², 27m², or 35m²) and **exterior cladding** (with or without wood siding).
- **Augmented Reality projection**: After configuration, users can visualize the selected yurt in their real environment using AR.
- **Wonder Button integration**: The AR experience includes a **Wonder Button** redirecting to the product page.
- **Fallback viewer**: If AR is not supported, a web-based viewer allows product exploration.
- **Seamless experience**: Fully integrated with **Wonder Shop**, ensuring smooth performance across devices.

## **Technical Stack**

- **Web Technologies**: HTML, CSS, JavaScript
- **Augmented Reality**: WebAR through Wonder Shop
- **Localization**: `i18n.js` for multi-language support

## **Project Structure**

```bash
yourte-in-garden-selector/
 ├── index.html                  # Main interface for the selection steps
 ├── main.js                    # Core initialization logic
 ├── app.js                     # Handles navigation and state management
 ├── selectionManager.js        # Manages user selections
 ├── stepManager.js             # Controls transitions between steps
 ├── imageManager.js            # Dynamically updates yurt previews
 ├── ar-button.js               # Triggers the AR projection
 ├── i18n.js                    # Localization engine
 ├── carrousel.js               # Interactive swiper for option browsing
 ├── assets/                    # Images, 3D models, and other resources
 │   ├── fonts/
 │   ├── images/
 │   ├── videos/
 │   └── configuratorData.js    # Configuration data for yurts
 ├── locales/                   # Translation files
 │   ├── fr.json
 │   ├── es.json
 │   └── de.json
 └── styles/
     ├── style.css
     ├── header.css
     ├── footer.css
     ├── tutorial.css
     ├── step.css
     └── carrousel.css
```

## **Getting Started**

1. **Clone the repository**:

```bash
git clone https://github.com/wonder-partners/yourte-in-garden-selector.git
```

2. **Install dependencies**:

```bash
npm install
```

## **Usage**

1. Scan the **QR Code** to launch the selector.
2. Choose the **yurt size** (17m², 27m², 35m²).
3. Select the **exterior finish** (with or without wood cladding).
4. Visualize the configured yurt in **Augmented Reality**.
5. Access the product page via the **Wonder Button**.

---

This tool brings the product discovery process to life, offering an **immersive, intuitive, and mobile-first experience**, fully integrated within **Wonder Shop**. 🌿✨
