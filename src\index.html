<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="./assets/images/favicon.png" />
    <title data-i18n="headerTitle">La Yourte’in Garden</title>
    <link rel="apple-touch-icon" href="./assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="180x180" href="./assets/images/favicon.png">

    <link rel="stylesheet" href="./styles/style.css" />
    <script type="module" src="./main.js"></script>
    <script></script>
  </head>
  <body>
    <header>
      <img
        src="./assets/images/logo-yourteingarden.svg"
        alt="logo yourteingarden" />
    </header>
    <main>
    <div id="intro-screen">
      <div class="intro-content">
        <div
          class="logo"
          alt="logo"
          data-i18n-attr="alt"
          data-i18n="logoImageAlt"></div>
        <p data-i18n="loadingMessage">Chargement...</p>
      </div>
    </div>

      <div id="tutorial" class="">
        <p data-i18n="introTxt">
          Visualisez la yourte en 3D dans votre jardin ET n'oubliez pas de visiter l'intérieur.
        </p>
        <div class="video-container">
          <video class="video-tuto" autoplay loop muted playsinline>
            <source src="assets/videos/video-tuto.mp4" type="video/mp4" />
          </video>
        </div>

        <button id="buttonStart" data-i18n="btnStart">Commencer</button>
      </div>

      <!-- Étape 1 -->
      <div id="step1" class="step-container hidden">
        <div class="stepper-container">
          <div class="steps-image">
            <div class="enable-step"></div>
            <div class="disable-step"></div>
            <div class="disable-step"></div>
          </div>
          </div>
          <div id="step-info-spacer"></div>
          <p data-i18n="titleStep1">Sélectionner un module</p>        
        <div class="carousel-container">
          <div class="swiper">
            <div class="swiper-wrapper"></div>
          </div>
        </div>
      </div>

      <!-- Étape 2 -->
      <div id="step2" class="step-container hidden">
        <div class="step-info-container">
          <div class="stepper-container">
          <div class="steps-image">
            <div class="enable-step"></div>
            <div class="enable-step"></div>
            <div class="disable-step"></div>
          </div>
          <button
            class="previous-step"
            data-i18n="btnPrevious"
            data-prev="step1">
            Étape précédente
          </button></div>
          <p data-i18n="titleStep2">Sélectionner la couleur des portes</h2>
        </div>
        <div class="carousel-container">
          <div class="swiper">
            <div class="swiper-wrapper"></div>
          </div>
        </div>
      </div>

      <!-- Étape 3 -->
      <div id="step3" class="step-container hidden">
        <div class="step-info-container">
          <div class="stepper-container">
          <div class="steps-image">
            <div class="enable-step"></div>
            <div class="enable-step"></div>
            <div class="enable-step"></div>
          </div>
          <button
            class="previous-step"
            data-i18n="btnPrevious"
            data-prev="step3">
            Étape précédente
          </button>
          </div>
          <p data-i18n="titleStep3">Sélectionner un module</p>
        </div>
        <div id="final-selection">
          <div id="final-preview">
            <div id="final-img"></div>
            </div>          
        </div>
        <div id="btn-ra-container">
          <button id="btnRA" data-i18n="btnRA">Essayer chez moi</button>
        </div>
      </div>
    </main>
    <footer>
      <a href="https://wonder-partners.com/">
        Powered by Wonder Partner’s
        <img
          src="./assets/images/footer-gradient.png"
          alt="Wonder Partner gradient" />
      </a>
    </footer>
  </body>
</html>
