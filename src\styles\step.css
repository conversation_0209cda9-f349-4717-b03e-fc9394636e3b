.step-container {
  padding: 0 22px 22px 22px;
  display: flex;
  flex-direction: column;
  text-align: center;
  width: 100%;
  height: 100%;
  max-height: 100%;
  max-height: 80%; /* Garde une hauteur max raisonnable */
  gap: 20px;
  padding-bottom: 20px;
  max-height: 100%;
}

#step3 {
  max-height: 100%; /* Garde une hauteur max raisonnable */
}

.step-info-container {
  -webkit-overflow-scrolling: touch;
  display: flex;
  flex-direction: column;
  align-items: left;
  width: 100%;
  gap: 10px;
}

.steps-image {
  display: flex;
  flex-direction: row;
  align-items: left;
  position: relative;
  width: 100%;
  height: 4px;
  gap: 10px;
}

.enable-step {
  top: 0;
  left: 0;
  width: calc((100%) / 3);
  height: 4px;
  background-color: var(--accent-color);
}

.disable-step {
  top: 0;
  left: 0;
  width: calc((100%) / 3);
  height: 4px;
  background-color: var(--secondary-color);
}

.previous-step {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
  min-width: 200px;

  background-color: var(--primary-color);
  color: var(--text-primary);
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.previous-step::before {
  content: "";
  display: inline-block;
  background-image: url(../assets/images/arrow-back.png);
  color: var(--text-primary);
  width: 19px;
  height: 19px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin: 0;
  padding: 0;
}

/* Final selection */
#final-selection {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: pan-x;
  max-height: 100%;
  min-height: 0;
}

#final-preview {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: min(90%, 500px);
  height: 100%;
  min-height: 0;
  max-height: 100%;
  border-radius: 12px;
  background-color: var(--secondary-color);
  padding: 14px;
}

#final-img {
  flex-direction: column;
  width: 100%;
  flex-wrap: nowrap;
  display: flex;
  height: 100%;
  justify-content: space-between;
}

#final-img h1 {
  text-align: right;
  width: 100%;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
}

.layer {
  position: absolute;
  width: 100%;
  max-width: 100%;
  height: auto;
  max-height: 100%;
  object-fit: contain;
}

.final-Layer {
  position: relative;
  width: 100%;
  max-width: 100%;
  height: auto;
  max-height: 100%;
  object-fit: contain;
  min-height: 0;
  border-radius: 12px;

  object-fit: cover;
  object-position: bottom center;
  background-repeat: no-repeat;
}

.module-layer {
  z-index: 1;
}

.plan-layer {
  z-index: 2;
}

.porte-layer {
  z-index: 3;
}

#btn-ra-container {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0 22px;
  justify-content: center;
}

#step-info-spacer {
  height: 10px;
}

/* media for tablet */
@media (min-width: 768px) {
  .stepper-container {
    gap: 20px;
    display: flex;
    flex-direction: column;
  }

  .step-container {
    padding: 0 80px 20px 80px;
  }

  .previous-step {
    /* tablet/Button_back */
    font-family: Nunito;
    font-size: 28px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    min-width: 300px;
  }

  #final-selection {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    touch-action: pan-x;
    /* height: 100%; */
    max-height: 60vh;
    min-height: 0;
  }

  #final-preview {
    width: 100%;
    height: 100%;
    min-height: 0;
    max-height: 100%;
  }
}

/*  Media for desktop */
@media (min-width: 1280px) {
  .step-container {
    padding: 0 160px 20px 160px;
  }

  .steps-image {
    width: 50%;
  }

  .stepper-container {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    justify-content: center;
  }

  #step1 .stepper-container {
    width: 100%;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    justify-content: center;
  }

  .swiper-slide p {
    font-family: "Funnel Display";
    font-size: 28.8px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    justify-content: space-between;
  }

  .previous-step {
    position: absolute;
    left: 180px;
    font-family: "Nunito";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  #final-preview {
    width: 35%;
  }
}
