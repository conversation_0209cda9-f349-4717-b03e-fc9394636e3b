import I18n from "./i18n.js";
import App from "./app.js";
import { configuratorData } from "./assets/configuratorData.js";

const i18n = new I18n();

document.addEventListener("DOMContentLoaded", async () => {
  await i18n.loadTranslations();
  i18n.applyTranslations();

  // Add event listener to the start button for tutorial
  const intro = document.getElementById("intro-screen");

  const tutorial = document.getElementById("tutorial");
  const buttonStart = document.getElementById("buttonStart");
  buttonStart.addEventListener("click", () => {
    tutorial.classList.add("fade-out");
    setTimeout(() => {
      tutorial.classList.remove("fade-out");
      tutorial.classList.add("hidden");
      initApp();
    }, 500);
  });

  await preloadImages(configuratorData);
  setTimeout(() => {
    intro.classList.add("intro-hidden");
    setTimeout(() => {
      intro.remove();
    }, 800);
  }, 1000);
});

async function initApp() {
  const app = new App(i18n, configuratorData);
  window.app = app;
}

function preloadImages(config) {
  const imageUrls = [
    ...config.step1.options.map((opt) => opt.image),
    ...Object.values(config.step2.options)
      .flat()
      .map((opt) => opt.image),
  ];

  return Promise.all(
    imageUrls.map(
      (src) =>
        new Promise((resolve) => {
          const img = new Image();
          img.onload = img.onerror = resolve;
          img.src = src;
        })
    )
  );
}
