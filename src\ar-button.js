export default class ARButton {
  constructor(app) {
    this.app = app;
    this.selectionManager = app.selectionManager;
    this.configuratorData = app.configuratorData;
    this.button = document.getElementById("btnRA");
    this.button.addEventListener("click", () => {
      this.redirectAR();
    });
  }

  redirectAR() {
    const size = this.selectionManager.getSelection("step1");
    const option = this.selectionManager.getSelection("step2");

    if (!size || !option) {
      console.warn("⚠️ Il manque un choix pour afficher le modèle final !");
      return;
    }

    const finalKey = size + "_" + option;
    const uid = this.configuratorData.step3.options[finalKey];
    if (!uid) {
      console.warn(
        "⚠️ Problème lors du chargement du lien AR ! avec " + finalKey
      );
      return;
    }

    const arUrl = `https://xr.wonder-shop.net/ar/${uid}`;
    window.open(arUrl, "_blank");
  }
}
