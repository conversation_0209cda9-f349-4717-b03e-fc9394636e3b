import Swiper from "swiper";
import { Manipulation } from "swiper/modules";
import "swiper/css";

export default class Carousel {
  constructor(app, containerElement) {
    this.app = app;
    this.container = containerElement;
    const swiperElement = containerElement.querySelector(".swiper");

    this.swiper = new Swiper(swiperElement, {
      modules: [Manipulation],
      direction: "horizontal",
      loop: false,
      spaceBetween: 16,
      slidesOffsetBefore: 0,
      slidesPerView: "auto",
      slidesOffsetAfter: 0,
    });

    window.addEventListener("resize", () => {
      const isMobile = window.innerWidth < 768;
      this.swiper.params.centeredSlides = isMobile;
      this.swiper.update();
    });
  }

  generateCarousel(items) {
    this.items = items;
    this.currentIndex = 0;

    items.forEach((item, index) => {
      const itemElement = document.createElement("div");
      itemElement.classList.add("swiper-slide");
      if (index === 0) itemElement.classList.add("active");

      itemElement.innerHTML = `
      <img src="${item.image}" alt="${item.label}"> 
        <h1>${item.label}</h1>
        `;
      this.swiper.appendSlide(itemElement);
    });

    this.swiper.on("click", (swiper, event) => {
      const clickedSlide = swiper.clickedSlide;

      if (clickedSlide) {
        this.selectItem(this.swiper.clickedIndex);
      }
    });
  }

  selectItem(index) {
    this.currentIndex = index;
    const stepKey =
      this.app.stepManager.steps[this.app.stepManager.currentStep];

    this.app.selectOption(stepKey, this.items[this.currentIndex].key);
  }

  destroy() {
    if (this.swiper) {
      this.swiper.removeAllSlides();
      this.swiper.destroy();

      window.removeEventListener("resize", () => {
        const isMobile = window.innerWidth < 768;
        this.swiper.params.centeredSlides = isMobile;
        this.swiper.update();
      });
    }
  }
}
