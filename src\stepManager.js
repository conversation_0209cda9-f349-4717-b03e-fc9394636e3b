export default class StepManager {
  constructor(app) {
    this.app = app;
    this.steps = Object.keys(app.configuratorData);
    this.currentStep = 0;
  }

  showStep(index) {
    const stepKey = this.steps[index];
    const currentStepElement = document.getElementById(stepKey);
    const previousStepElement = document.querySelector(
      ".step-container:not(.hidden)"
    );

    if (previousStepElement) {
      previousStepElement.classList.add("fade-out");

      setTimeout(() => {
        previousStepElement.classList.add("hidden");
        previousStepElement.classList.remove("fade-out");
        this.generateElement(stepKey, currentStepElement);

        if (currentStepElement) {
          currentStepElement.classList.remove("hidden");
          currentStepElement.classList.add("fade-in");

          setTimeout(() => {
            currentStepElement.classList.remove("fade-in");
          }, 500);
        }

        // Génération des options après la transition
      }, 300); // On attend la fin de l'animation avant d'afficher la nouvelle étape
    } else {
      // Si aucune étape précédente (première étape), on affiche directement
      if (currentStepElement) {
        currentStepElement.classList.remove("hidden");
      }
      this.generateElement(stepKey, currentStepElement);
    }
  }

  generateElement(stepKey, currentStepElement) {
    {
      if (stepKey === "step3") {
        this.app.generateFinalSelection();
      } else {
        this.app.generateOptions(stepKey, currentStepElement);
      }
    }
  }

  nextStep() {
    if (this.currentStep < this.steps.length - 1) {
      this.currentStep++;
      this.showStep(this.currentStep);
    }
  }

  prevStep() {
    if (this.currentStep > 0) {
      this.currentStep--;
      //remove option from selectionManager
      this.app.selectionManager.removeOption(this.steps[this.currentStep]);
      this.showStep(this.currentStep);
    }
  }

  getCurrentStepKey() {
    return this.steps[this.currentStep];
  }
}
