@import "header.css";
@import "footer.css";
@import "tutorial.css";
@import "step.css";
@import "carrousel.css";
@import url("intro.css");

:root {
  --white: #fafafa;
  --light-green: #bcbcad;
  --beige: #dfdece;
  --orange: #f15025;
  --green: #112c22;

  --primary-color: var(--beige);
  --secondary-color: var(--light-green);
  --accent-color: var(--orange);

  --text-primary: var(--green);
  --text-secondary: var(--white);

  --button-icon-size: 25px;
}

/* Reset de base */
* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -ms-touch-action: none;
  box-sizing: border-box;

  /* Éviter le zoom sur les éléments HTML */
  touch-action: manipulation;
}

/*Import des polices*/
@font-face {
  font-family: "FunnelDisplay";
  src: url(../assets/fonts/FunnelDisplay/FunnelDisplay-VariableFont_wght.ttf)
    format("opentype");
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: "Nunito";
  src: url(../assets/fonts/Nunito/Nunito-Italic-VariableFont_wght.ttf)
    format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Nunito";
  src: url(../assets/fonts/Nunito/Nunito-VariableFont_wght.ttf)
    format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Trueno";
  src: url(../assets/fonts/Trueno/TruenoLt.otf) format("truetype");
  font-weight: 300;
  font-style: normal;
}

/*Style des textes*/
h1 {
  font-family: "FunnelDisplay";
  font-size: 2rem;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-align: right;
  color: var(--text-primary);
}

h2 {
  font-family: "FunnelDisplay";
  font-size: 1.5rem;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  text-align: right;
  color: var(--text-primary);
}

p {
  font-family: "FunnelDisplay";
  font-weight: 700;
  font-size: 1.125rem;
  color: var(--text-primary);
}

/* Style des boutons */
button {
  width: 100%;
  max-width: 210px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  background-color: var(--accent-color);
  border: none;
  border-radius: 12px;

  color: var(--text-secondary);
  text-align: right;
  font-family: "FunnelDisplay";
  font-size: 1rem;
  font-style: normal;
  font-weight: 700;
  line-height: 31px;
}

button:hover {
  cursor: pointer;
}

#btnRA::after {
  content: "";
  display: inline-block;
  background-image: url(../assets/images/icon-ra.png);
  background-color: #f15025;
  width: var(--button-icon-size);
  height: var(--button-icon-size);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-left: 10px;
}

.logo {
  background-image: url("../assets/images/logo.svg");
}

body {
  font-family: "Nunito";
  font-weight: 400;
  background: var(--beige);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100vh;
  height: 100svh;
  height: 100dvh;

  max-height: 100vh;
  overflow: hidden;
}

main {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  min-height: 0;
  overflow: hidden;
}

/* Animation de fondu */
.fade-out {
  opacity: 0;
  transition: opacity 0.3s ease-out;
  pointer-events: none;
}

.fade-in {
  opacity: 1;
  animation: fadeIn 0.5s forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.hidden {
  display: none !important;
}

/* media for tablet */
@media (min-width: 768px) {
  h1 {
    font-family: "FunnelDisplay";
    font-size: 2rem;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }

  button {
    width: 360px;

    max-width: 400px;
    height: 54px;
    justify-content: center;
    align-items: center;
    font-family: "FunnelDisplay";
    font-size: 28px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }

  p {
    /* tablet/Body_t */
    font-family: "FunnelDisplay";
    font-size: 32px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }

  #buttonStart::after {
    width: 35px;
    height: 35px;
    margin-left: 16px;
  }

  #btnRA::after {
    width: 35px;
    height: 35px;
    margin-left: 16px;
  }
}

/*  Media for desktop */
@media (min-width: 1280px) {
  header {
    padding-bottom: 55px;
  }

  /*Style des textes*/
  h1 {
    font-family: "FunnelDisplay";
    font-size: 2rem;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    text-align: right;
    color: var(--text-primary);
  }

  h2 {
    font-family: "FunnelDisplay";
    font-size: 1.5rem;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    text-align: right;
    color: var(--text-primary);
  }

  p {
    font-family: "FunnelDisplay";
    font-weight: 700;
    font-size: 1.125rem;
    color: var(--text-primary);
  }

  button {
    width: 100%;
    height: 31px;
    max-width: 210px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    background-color: var(--accent-color);
    border: none;
    border-radius: 12px;

    color: var(--text-secondary);
    text-align: right;
    font-family: "FunnelDisplay";
    font-size: 1rem;
    font-style: normal;
    font-weight: 700;
    line-height: 31px;
  }

  #btnRA::after {
    width: var(--button-icon-size);
    height: var(--button-icon-size);
    margin-left: 10px;
  }

  #buttonStart::after {
    width: var(--button-icon-size);
    height: var(--button-icon-size);
    margin-left: 10px;
  }
}
