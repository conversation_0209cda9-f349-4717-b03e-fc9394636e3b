import { configuratorData } from "./assets/configuratorData.js";
import Carousel from "./carrousel.js";
import StepManager from "./stepManager.js";
import SelectionManager from "./selectionManager.js";
import ARButton from "./ar-button.js";

export default class App {
  constructor(i18n) {
    this.i18n = i18n;
    this.configuratorData = configuratorData;
    this.selectionManager = new SelectionManager();
    this.stepManager = new StepManager(this);
    this.arButton = new ARButton(this);
    this.init();
  }

  init() {
    this.stepManager.showStep(this.stepManager.currentStep);
    this.addEventListeners();
  }

  addEventListeners() {
    document.querySelectorAll(".previous-step").forEach((button) => {
      button.addEventListener("click", () => this.stepManager.prevStep());
    });

    document
      .getElementById("buttonStart")
      .addEventListener("click", () => this.stepManager.nextStep());
  }

  selectOption(step, value) {
    this.selectionManager.selectOption(step, value);
    this.stepManager.nextStep();
  }

  generateOptions(stepKey, element) {
    const stepData = this.configuratorData[stepKey];

    const previousChoice = stepData.dependencies
      ? this.selectionManager.getSelection(stepData.dependencies)
      : null;
    const options = previousChoice
      ? stepData.options[previousChoice]
      : stepData.options;

    if (!options) return;

    const translatedOptions = options.map((option) => ({
      ...option,
      label: this.i18n.getTranslation(option.labelKey),
    }));

    // Générer un nouveau carrousel avec les labels traduits
    if (this.carousel) {
      this.carousel.destroy();
    }

    this.carousel = new Carousel(this, element);
    this.carousel.generateCarousel(translatedOptions);
  }

  generateFinalSelection() {
    const size = this.selectionManager.getSelection("step1");
    const option = this.selectionManager.getSelection("step2");

    if (!size || !option) {
      console.warn("⚠️ Il manque un choix pour afficher le modèle final !");
      return;
    }

    //get images from configuratorData
    const sizeLabelKey = configuratorData.step1.options.find(
      (opt) => opt.key === size
    )?.labelKey;

    const sizeText = this.i18n.getTranslation(sizeLabelKey);

    const yourteImage = configuratorData.step2.options[size].find(
      (opt) => opt.key === option
    )?.image;

    const claddingLabelKey = configuratorData.step2.options[size].find(
      (opt) => opt.key === option
    )?.labelKey;

    const claddingText = this.i18n.getTranslation(claddingLabelKey);

    if (!sizeText || !yourteImage || !claddingText) {
      console.warn("⚠️ Problème lors du chargement des images !");
      return;
    }

    const finalContainer = document.getElementById("final-img");
    finalContainer.innerHTML = `
        <img src="${yourteImage}" class="module-layer final-Layer" alt="Module">     
        <div class="final-text">
        <h1>${sizeText}</h1>
        <h1>${claddingText}</h1>
        </div>`;
  }

  createImagePreview() {
    const size = this.selectionManager.getSelection("step1");
    const feature = this.selectionManager.getSelection("step2");

    console.log(configuratorData.step2.options);
    const sizeImage = configuratorData.step1.options.find(
      (opt) => opt.key === size
    )?.image;
    const featureImage = configuratorData.step2.options[size].find(
      (opt) => opt.key === feature
    )?.image;

    return `  
        ${sizeImage ? `<img src="${sizeImage}" class="layer module-layer" alt="Step 1">` : ""}
        ${featureImage ? `<img src="${featureImage}" class="layer porte-layer" alt="Step 2">` : ""}
    `;
  }
}
