import { defineConfig } from "vite";
import mkcert from "vite-plugin-mkcert";
import { viteStaticCopy } from "vite-plugin-static-copy";
import { resolve } from "path";

export default defineConfig({
  plugins: [
    mkcert(),
    viteStaticCopy({
      targets: [
        {
          src: "assets/*", // Le dossier source
          dest: "./assets", // Le dossier dans le build
        },
        {
          src: "locales/*", // Le dossier source
          dest: "./locales", // Le dossier dans le build
        },
      ],
    }),
  ],
  root: "src",
  base: "/selector/",
  server: {
    open: "/index.html", // Ouvrir index.html au démarrage
    https: true,
    headers: {
      "Cross-Origin-Embedder-Policy": "require-corp",
      "Cross-Origin-Opener-Policy": "same-origin",
    },
  },
  build: {
    rollupOptions: {
      input: {
        main: resolve(__dirname, "src/index.html"), // Page d'accueil
        404: resolve(__dirname, "src/404.html"), // Page d'erreur 404
      },
      output: {
        manualChunks(id) {
          if (id.includes("node_modules")) {
            return "vendor";
          }
        },
      },
    },
    outDir: "../public/selector",
    emptyOutDir: true,
    sourcemap: false,
  },
});
