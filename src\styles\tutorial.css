#tutorial {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  justify-content: space-evenly;
  width: 100%;
  height: 100%;
  max-height: 100%;
  gap: 50px;
  padding: 25px;
  text-align: center;
  overflow: hidden;
}

.video-container {
  position: relative;
  width: 100%;
  height: auto;
}

.video-tuto {
  width: 100%;
  height: auto;
  max-height: 100%;
  object-fit: cover;
  position: relative;
  top: 0;
  left: 0;
}

#buttonStart::after {
  content: "";
  display: inline-block;
  background-image: url(../assets/images/icon-star.png);
  width: var(--button-icon-size);
  height: var(--button-icon-size);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-left: 10px;
}

@media (min-width: 768px) {
  #tutorial {
    padding: 0 80px 20px 80px;
  }

  #buttonStart::after {
    width: 35px;
    height: 35px;
  }

  .video-tuto {
    width: 100%;
    max-width: 800px;
    max-height: 400px;

    height: 100%;
    object-fit: contain;
    position: relative;
    top: 0;
    left: 0;
  }
  .video-container {
    position: relative;
    width: 100%;
    min-height: 0;
    height: auto;
  }
}

/*  Media for desktop */
@media (min-width: 1280px) {
  #tutorial {
    padding: 0 160px 20px 160px;
    max-height: 100%;
  }

  #buttonStart::after {
    width: 35px;
    height: 35px;
  }
}
