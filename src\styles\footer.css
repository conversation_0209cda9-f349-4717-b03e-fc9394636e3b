footer {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 40px;
  width: 100%;
  max-height: 40px;
}

footer a {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 10px;
  gap: 8.68px;
}

a {
  color: var(--text-primary);
  font-family: "Trueno";
  font-weight: 300;
  font-size: 11px;
  text-decoration: none;
}

/* media for tablet */
@media (min-width: 768px) {
  footer {
    height: 70px;
    min-height: 70px;
  }
  footer a {
    text-align: center;
    font-family: Trueno;
    font-size: 19.25px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    gap: 15.19px;
  }
}

@media (min-width: 1280px) {
  footer {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 40px;
    width: 100%;
    max-height: 40px;
  }

  footer a {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 10px;
    max-height: 40px;
    font-family: "Trueno";
    font-weight: 300;
    font-size: 11px;
    text-decoration: none;
    gap: 8.68px;
  }
}
