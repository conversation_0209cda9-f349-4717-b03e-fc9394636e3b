#intro-screen {
  position: fixed;
  inset: 0;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  z-index: 9999;

  opacity: 1;
  visibility: visible;
  transition:
    opacity 0.8s ease,
    visibility 0.8s ease;
}

/* Disparition */
#intro-screen.intro-hidden {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

#intro-screen .logo {
  width: 100%;
  height: 150px;
  margin: 0 auto 1rem;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  animation: pulse 1.5s ease-in-out infinite;
  background-repeat: no-repeat;
  background-size: contain;
  transition: var(--transition-theme);
}

.intro-content {
  display: flex;
  flex-direction: column;
  text-align: center;
  animation: fadeInUp 1s ease-out forwards;
  gap: 20px;
}

.intro-content img {
  width: 130px;
  height: auto;
  margin-bottom: 1rem;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/*  Media for desktop */
@media (min-width: 1280px) {
  .intro-content {
    gap: 50px;
  }
}
